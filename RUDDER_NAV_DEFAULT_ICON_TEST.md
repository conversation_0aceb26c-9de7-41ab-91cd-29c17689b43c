# 舵式导航中间项默认图片功能测试

## 功能说明
为舵式导航的中间位置设置了默认图片，当用户没有自定义图片时，会显示默认图片。

**默认图片URL**: `https://prod.shukeyun.com/maintenance/deepfile/data/2025-08-15/upload_1e3fcd92856e16adc98e068f742d2b8d.png`

## 测试场景

### 1. 首次切换到舵式样式
**操作步骤**:
1. 打开商店导航配置页面
2. 选择"舵式样式"
3. 观察预览区域的中间导航项

**预期结果**:
- 中间位置显示默认图片
- 图片应该居中显示
- hover时显示"更换图片"

### 2. 调整导航数量
**操作步骤**:
1. 在舵式样式下，切换导航数量（3个 ↔ 5个）
2. 观察中间位置的图片是否保持

**预期结果**:
- 中间位置始终显示默认图片（如果用户未自定义）
- 图片位置正确居中

### 3. 图片上传功能
**操作步骤**:
1. 点击中间位置的"添加图标"或现有图片
2. 选择一张图片上传
3. 上传成功后观察显示效果

**预期结果**:
- 上传过程中显示"上传中..."状态
- 上传成功后显示用户选择的图片
- hover时显示"更换图片"

### 4. 重置功能
**操作步骤**:
1. 在舵式样式下修改中间位置的图片
2. 点击"重置"按钮
3. 观察中间位置是否恢复默认图片

**预期结果**:
- 中间位置恢复显示默认图片

## 代码修改点

### 1. 主组件 (index.tsx)
- `buildRudderDefault()`: 设置默认图片
- `resizeRudderList()`: 保持默认图片
- 添加 `DEFAULT_CENTER_ICON` 常量

### 2. 表单组件 (RudderNavForm.tsx)
- `adjustRudderByCount()`: 设置默认图片
- 添加 `DEFAULT_CENTER_ICON` 常量
- 图片上传功能替换弹窗选择

### 3. 样式文件 (index.less)
- 添加 `.centerIconRow` 样式实现图标居中

## 圆形图标功能

### 新增功能
- **中间导航项专用圆形样式**：只有中间位置的导航图标显示为圆形
- **其他导航项保持原样**：左右两侧的导航图标不使用圆形样式
- **图片适应性**：图片100%适应容器，使用 `object-fit: cover` 不裁剪变形

### 样式实现
1. **新增样式类** `.center_circle_img`：
   - 设置 `border-radius: 50%` 实现圆形
   - 使用 `object-fit: cover` 确保图片完美适应圆形容器
   - 保持64x64px的尺寸

2. **专用渲染函数** `renderCenterIcon`：
   - 为中间导航项提供专门的图标渲染逻辑
   - 确保图片样式正确应用

3. **预览区域同步**：
   - 预览区域的中间导航项也显示为圆形
   - 保持配置区域和预览区域的一致性

### 测试场景
1. **圆形显示**：中间导航项的图标应显示为圆形
2. **其他位置**：左右导航项保持原有的方形显示
3. **图片适应**：上传的图片应完美适应圆形容器，不变形不裁剪
4. **预览一致性**：配置区域和预览区域显示效果一致

## 注意事项
- 默认图片只在用户未设置自定义图片时显示
- 用户上传图片后会覆盖默认图片
- 重置功能会恢复默认图片
- 默认图片URL统一管理，便于后续维护
- **中间导航项使用圆形样式，其他位置保持原样**
- **图片使用 `object-fit: cover` 确保不变形**
