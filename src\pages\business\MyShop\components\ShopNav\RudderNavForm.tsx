import ProSvg from '@/common/components/ProSvg';
import { systemPageEnum } from '@/common/utils/enum';
import type { ProFormInstance, FormListActionType } from '@ant-design/pro-components';
import { ProFormList, ProFormText, ProFormSelect, ProFormRadio, ProForm, ProCard } from '@ant-design/pro-components';
import { Space, Form, Upload, message } from 'antd';
import type { FC, MutableRefObject } from 'react';
import type { RcFile } from 'antd/lib/upload';
import { useState } from 'react';
import { scenicHost } from '@/services/api';
import { getEnv } from '@/common/utils/getEnv';
import styles from './index.less';

// 中间导航项的默认图片
const DEFAULT_CENTER_ICON =
  'https://prod.shukeyun.com/maintenance/deepfile/data/2025-08-15/upload_1e3fcd92856e16adc98e068f742d2b8d.png';

// 子组件的通用props类型
type NavItemProps = {
  index: number;
  tabList: any[];
  setIsModalOpen: (index: number) => void;
  renderIcon: (src?: string) => JSX.Element;
  styles: any;
};

// 中间位置导航的特殊props类型
type CenterNavItemProps = Omit<NavItemProps, 'setIsModalOpen' | 'renderIcon'> & {
  onImageUpload: (index: number, imageUrl: string) => void;
  renderCenterIcon: (src?: string) => JSX.Element;
};

// 图片上传逻辑hook
const useImageUpload = (onImageUpload: (index: number, imageUrl: string) => void) => {
  const { FILE_HOST } = getEnv();
  const [uploading, setUploading] = useState<number | null>(null);

  const beforeUpload = (file: RcFile) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error('只允许上传 jpeg,png 格式的图片！');
    }
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('图片必须小于10MB!');
    }
    return isJpgOrPng && isLt10M;
  };

  const handleUpload = (index: number) => {
    if (uploading !== null) return; // 防止重复上传

    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/png, image/jpeg';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file && beforeUpload(file as RcFile)) {
        setUploading(index);
        const formData = new FormData();
        formData.append('file', file);

        // 使用fetch模拟Upload组件的上传逻辑
        fetch(scenicHost + '/aws/uploadFile', {
          method: 'POST',
          body: formData,
        })
          .then((response) => response.json())
          .then((response) => {
            if (response.data?.path) {
              onImageUpload(index, FILE_HOST + response.data.path);
              message.success('图片上传成功');
            } else {
              message.error('上传失败，请重试');
            }
          })
          .catch(() => {
            message.error('上传失败，请重试');
          })
          .finally(() => {
            setUploading(null);
          });
      }
    };
    input.click();
  };

  return { handleUpload, uploading };
};

// 中间位置导航组件
const CenterNavItem: FC<CenterNavItemProps> = ({
  index,
  tabList,
  styles,
  onImageUpload,
  renderCenterIcon,
}) => {
  const { handleUpload, uploading } = useImageUpload(onImageUpload);
  const isUploading = uploading === index;

  return (
    <div style={{ display: 'flex', alignItems: 'flex-start', gap: 16 }}>
      <ProForm.Item className={styles.icon} label={false}>
        <Space align="start">
          <ProFormText
            name="icon_active"
            hidden
            rules={[{ required: true, message: '请添加图标' }]}
          />
          {!tabList?.[index]?.icon_active ? (
            <div className={styles.addWrap}>
              <div
                className={styles.addBlock}
                onClick={() => !isUploading && handleUpload(index)}
                style={{
                  cursor: isUploading ? 'not-allowed' : 'pointer',
                  opacity: isUploading ? 0.6 : 1,
                }}
              >
                <div className={styles.addTextInside}>{isUploading ? '上传中...' : '添加图标'}</div>
              </div>
              <Form.Item noStyle shouldUpdate>
                {(form2) => {
                  const iconActiveErrors = form2.getFieldError(['shopNav', index, 'icon_active']);
                  const hasErrors = (iconActiveErrors?.length || 0) > 0;
                  return hasErrors ? <div className={styles.iconError}>请添加图标</div> : null;
                }}
              </Form.Item>
            </div>
          ) : (
            <div className={styles.iconWrap}>
              <div
                className={styles.iconBlock}
                onClick={() => !isUploading && handleUpload(index)}
                style={{
                  cursor: isUploading ? 'not-allowed' : 'pointer',
                  opacity: isUploading ? 0.6 : 1,
                }}
              >
                <div className={styles.centerIconRow}>
                  <div className={`${styles.circle}`}>
                    <div className={`${styles.center_circle_img}`}>
                      {renderCenterIcon(tabList?.[index]?.icon_active)}
                    </div>
                  </div>
                </div>
                <div className={styles.mask}>{isUploading ? '上传中...' : '更换图片'}</div>
              </div>
            </div>
          )}
        </Space>
      </ProForm.Item>
      <div style={{ display: 'flex', flexDirection: 'column', marginTop: 16, width: 320 }}>
        <Form.Item noStyle shouldUpdate>
          {(form) => (
            <ProFormRadio.Group
              name="linkType"
              label="链接"
              options={[
                { label: '内置链接', value: 'internal' },
                { label: '自定义链接', value: 'external' },
              ]}
              initialValue={'internal'}
              fieldProps={{
                onChange: () => {
                  // 切换链接类型时清空link字段
                  form.setFieldValue(['shopNav', index, 'link'], undefined);
                },
              }}
            />
          )}
        </Form.Item>
        <Form.Item noStyle shouldUpdate>
          {(form2) => {
            const linkType = form2.getFieldValue(['shopNav', index, 'linkType']) || 'internal';
            if (linkType === 'external') {
              return (
                <ProFormText
                  name="link"
                  placeholder="请输入自定义链接，如 https://example.com"
                  rules={[
                    { required: true, message: '请输入自定义链接' },
                    {
                      pattern: /^https?:\/\//i,
                      message: '链接需以 http:// 或 https:// 开头',
                    },
                  ]}
                />
              );
            }
            return (
              <ProFormSelect
                name="link"
                valueEnum={systemPageEnum}
                rules={[{ required: true, message: '请选择导航链接' }]}
              />
            );
          }}
        </Form.Item>
      </div>
    </div>
  );
};

// 普通位置导航组件
const RegularNavItem: FC<NavItemProps & { tabListLength: number }> = ({
  index,
  tabList,
  setIsModalOpen,
  renderIcon,
  styles,
  tabListLength,
}) => (
  <div style={{ display: 'flex', alignItems: 'flex-start', gap: 16 }}>
    <ProForm.Item className={styles.icon} label={false}>
      <Space align="start">
        <ProFormText
          name="icon_active"
          hidden
          rules={[{ required: true, message: '请添加图标' }]}
        />
        <ProFormText name="icon" hidden rules={[{ required: true, message: '请添加图标' }]} />
        {!(tabList?.[index]?.icon_active || tabList?.[index]?.icon) ? (
          <div className={styles.addWrap}>
            <div className={styles.addBlock} onClick={() => setIsModalOpen(index)}>
              <div className={styles.addTextInside}>添加图标</div>
            </div>
            <Form.Item noStyle shouldUpdate>
              {(form2) => {
                const iconErrors = form2.getFieldError(['shopNav', index, 'icon']);
                const iconActiveErrors = form2.getFieldError(['shopNav', index, 'icon_active']);
                const hasErrors =
                  (iconErrors?.length || 0) > 0 || (iconActiveErrors?.length || 0) > 0;
                return hasErrors ? <div className={styles.iconError}>请添加图标</div> : null;
              }}
            </Form.Item>
          </div>
        ) : (
          <div className={styles.iconWrap}>
            <div className={styles.iconBlock} onClick={() => setIsModalOpen(index)}>
              <div className={styles.iconsRow}>
                <div className={`${styles.circle}`}>
                  <div className={`${styles.circle_img}`}>
                    {renderIcon(tabList?.[index]?.icon_active)}
                  </div>
                </div>
                <div className={styles.circle}>
                  <div className={`${styles.circle_img}`}>{renderIcon(tabList?.[index]?.icon)}</div>
                </div>
              </div>
              <div className={styles.mask}>替换</div>
            </div>
            <div className={styles.labelsRow}>
              <span>选中</span>
              <span>未选中</span>
            </div>
          </div>
        )}
      </Space>
    </ProForm.Item>
    <div style={{ display: 'flex', flexDirection: 'column', marginTop: 16 }}>
      <ProFormText
        labelCol={{ span: 42 }}
        width={260}
        name="name"
        label="名称"
        fieldProps={{ maxLength: 4, showCount: true }}
        rules={[{ required: true, message: '请输入导航名称' }]}
      />
      <ProFormSelect
        width={260}
        name="link"
        label="链接"
        valueEnum={systemPageEnum}
        disabled={index === 0 || index === tabListLength - 1}
        rules={
          index === 0 || index === tabListLength - 1
            ? []
            : [{ required: true, message: '请选择导航链接' }]
        }
      />
    </div>
  </div>
);

type RudderNavFormProps = {
  formRef: MutableRefObject<ProFormInstance | undefined>;
  listActionRef: MutableRefObject<FormListActionType | undefined>;
  tabList: any[];
  setTabList: (updater: any) => void;
  setIsModalOpen: (index: number) => void;
  color?: string;
  currentCount?: number;
  onCountChange?: (count: number, nextList: any[]) => void;
};

// 根据目标数量重排舵式列表：固定首/中/尾，其余位置留空或在等长时保留
const adjustRudderByCount = (currentList: any[], count: number) => {
  const target = Math.max(count, 3);
  const result = new Array(target).fill(null).map(() => ({ name: '', icon: '', icon_active: '' }));
  const oldFirst = currentList?.[0] ?? { name: '首页', icon: '1_1', icon_active: '1_1_' };
  const oldMid = currentList?.[Math.floor((currentList?.length || 3) / 2)] ?? {
    name: '商品工坊',
    icon: '',
    icon_active: DEFAULT_CENTER_ICON,
  };
  const oldLast = currentList?.[(currentList?.length || 1) - 1] ?? {
    name: '我的',
    icon: '1_4',
    icon_active: '1_4_',
  };
  const mid = Math.floor(target / 2);
  result[0] = { ...oldFirst };
  result[mid] = { ...oldMid };
  result[target - 1] = { ...oldLast };
  // 如果等长，保留非首/中/尾的原位内容；扩容时这些位为空（满足 3->5 填充在 1 和 3）
  if ((currentList?.length || 0) === target) {
    for (let i = 1; i < target - 1; i += 1) {
      if (i === mid) continue;
      result[i] = currentList[i] ?? { name: '', icon: '', icon_active: '' };
    }
  }
  return result;
};

const RudderNavForm: FC<RudderNavFormProps> = ({
  formRef,
  listActionRef,
  tabList,
  setTabList,
  setIsModalOpen,
  color,
  currentCount,
  onCountChange,
}) => {
  const renderIcon = (src?: string) => {
    if (!src) return <div className={styles.iconPlaceholder} />;
    return typeof src === 'string' && src.includes('http') ? (
      <img
        src={src}
        style={{
          maxWidth: '100%',
          maxHeight: '100%',
          width: 'auto',
          height: 'auto',
          objectFit: 'contain',
        }}
      />
    ) : (
      <ProSvg src={src} color={color} />
    );
  };

  // 专门为中间导航项渲染圆形图标
  const renderCenterIcon = (src?: string) => {
    if (!src) return <div className={styles.iconPlaceholder} />;
    return typeof src === 'string' && src.includes('http') ? (
      <img
        src={src}
        style={{
          maxWidth: '100%',
          maxHeight: '100%',
          width: 'auto',
          height: 'auto',
          objectFit: 'contain',
        }}
      />
    ) : (
      <ProSvg src={src} color={color} width="100%" height="100%" />
    );
  };

  return (
    <>
      <ProFormRadio.Group
        name="rudderCount"
        label="导航数量"
        options={[
          { label: '3个', value: 3 },
          { label: '5个', value: 5 },
        ]}
        initialValue={currentCount ?? 3}
        fieldProps={{
          onChange: (e) => {
            const count = e.target.value;
            const current = formRef.current?.getFieldValue('shopNav') || [];
            const next = adjustRudderByCount(current, count);
            formRef.current?.setFieldsValue({ shopNav: next });
            setTabList(next);
            onCountChange?.(count, next);
          },
        }}
      />
      <Form.Item noStyle shouldUpdate>
        {(form) => {
          const count = form.getFieldValue('rudderCount') ?? 3;
          const mid = Math.floor(((tabList?.length || count) as number) / 2);
          return (
            <ProFormList
              label="导航设置"
              className={styles.navList}
              name="shopNav"
              creatorButtonProps={false}
              actionRef={listActionRef as any}
              min={count}
              max={count}
              copyIconProps={false}
              actionRender={() => []}
              itemRender={({ listDom, action }, { index }) => {
                const isCenter = index === mid;
                const titleContent = isCenter ? (
                  <div style={{ lineHeight: '32px' }}>
                    导航 {index + 1}
                    <span style={{ color: '#969799', fontSize: '12px' }}>
                      （中心导航仅支持配置页面跳转的链接）
                    </span>
                  </div>
                ) : (
                  <div style={{ lineHeight: '32px' }}>导航 {index + 1}</div>
                );

                return (
                  <ProCard
                    bordered
                    title={titleContent}
                    headStyle={{ padding: '8px 24px' }}
                    extra={action}
                    type="inner"
                  >
                    {listDom}
                  </ProCard>
                );
              }}
            >
              {(_, index) => {
                const isCenter = index === mid;
                const commonProps = {
                  index,
                  tabList,
                  setIsModalOpen,
                  renderIcon,
                  styles,
                };

                const centerProps = {
                  index,
                  tabList,
                  styles,
                  renderCenterIcon,
                };

                const handleImageUpload = (uploadIndex: number, imageUrl: string) => {
                  setTabList((value: any) => {
                    const valueClone = structuredClone(value);
                    valueClone[uploadIndex].icon_active = imageUrl;
                    formRef?.current?.setFieldsValue({
                      shopNav: valueClone,
                    });
                    return valueClone;
                  });
                };

                return isCenter ? (
                  <CenterNavItem {...centerProps} onImageUpload={handleImageUpload} />
                ) : (
                  <RegularNavItem {...commonProps} tabListLength={tabList?.length || 0} />
                );
              }}
            </ProFormList>
          );
        }}
      </Form.Item>
    </>
  );
};

export default RudderNavForm;


