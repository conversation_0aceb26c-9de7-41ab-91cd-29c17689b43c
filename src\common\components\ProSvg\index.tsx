import { useEffect } from 'react';
import { getEnv } from '@/common/utils/getEnv';

export default ({ src, color, width, height }: any) => {
  // const host =
  //   process.env.NODE_ENV == 'development' ? '' : getEnv().ENV == 'prod' ? 'exchange/' : 'scenic/exchange/';
  useEffect(() => {
    const getEle = (id: any) => document.getElementById(id);
    const setSvg = () => {
      document.body.insertAdjacentHTML(
        'beforeend',
        `<svg
          id="svg"
          aria-hidden="true"
          style="position: absolute; width: 0px; height: 0px; overflow: hidden"
        ></svg>`,
      );
      return getEle('svg');
    };
    const svg: any = getEle('svg') || setSvg();
    if (!getEle(src)) {
      fetch(`tab_icon/${src}.svg`).then((res) => {
        res.text().then((data) => {
          // // 单色图标
          // data = data.replace(/fill="#\w+"/g, 'fill="currentColor"');
          // // 多色图标
          // data = data.replace(/fill="#\w+"/g, (match) => {
          //   // 获取匹配到的颜色值
          //   const colorValue = match.match(/#(\w+)/)[1]
          //   const opacityValue = parseInt(colorValue.slice(-2), 16) / 255
          //   return `fill="currentColor" opacity="${opacityValue}"`
          // })
          // 自动注入
          svg.insertAdjacentHTML(
            'beforeend',
            `<symbol id="${src}" ${data.slice(
              data.indexOf('<svg') + 4,
              data.indexOf('</svg>'),
            )} </symbol>`,
          );
        });
      });
    }
  }, []);
  return (
    <svg aria-hidden="true" style={{ width, height, color }}>
      <use xlinkHref={'#' + src} />
    </svg>
  );
};
