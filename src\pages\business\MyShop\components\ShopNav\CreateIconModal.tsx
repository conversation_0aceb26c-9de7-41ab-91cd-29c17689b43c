import type { FC } from 'react';
import { useMemo, useState, useCallback, useEffect } from 'react';
import { Modal, Form, Input, Space, Typography, Divider, message } from 'antd';
import { addOrUpdateStoreIcon } from '@/services/api/store';
import FileUpload from '@/common/components/FileUpload';

type IconInfo = {
  id?: string | number;
  name: string;
  icon: string; // 未选中
  icon_active: string; // 选中
};

type FileInfo = {
  id: string;
  fileUrl: string;
  filePath?: string;
  fileName?: string;
  fileType?: string;
  fileSize?: number;
};

type CreateIconModalProps = {
  open: boolean;
  onCancel: () => void;
  onOk?: (icon: IconInfo) => void;
  storeId?: string | number;
  editing?: IconInfo;
};

const CreateIconModal: FC<CreateIconModalProps> = ({ open, onCancel, onOk, storeId, editing }) => {
  const [form] = Form.useForm();
  const [activeFiles, setActiveFiles] = useState<FileInfo[]>([]);
  const [inactiveFiles, setInactiveFiles] = useState<FileInfo[]>([]);

  // 编辑模式下允许不重新上传，使用已有默认值
  const initialActive = useMemo(
    () =>
      editing?.icon_active ? [{ id: 'active', fileUrl: editing.icon_active } as FileInfo] : [],
    [editing?.icon_active],
  );

  const initialInactive = useMemo(
    () => (editing?.icon ? [{ id: 'inactive', fileUrl: editing.icon } as FileInfo] : []),
    [editing?.icon],
  );

  // 当编辑的图标变化时，重置状态和表单
  useEffect(() => {
    if (open) {
      // 重置文件上传状态
      setActiveFiles([]);
      setInactiveFiles([]);

      // 重置并设置表单初始值
      form.resetFields();
      if (editing?.name) {
        form.setFieldsValue({ name: editing.name });
      }
    }
  }, [open, editing, form]);

  const okDisabled = useMemo(() => {
    const values = form.getFieldsValue();
    const hasActive = activeFiles.length > 0 || initialActive.length > 0;
    const hasInactive = inactiveFiles.length > 0 || initialInactive.length > 0;
    return !values?.name || !hasActive || !hasInactive;
  }, [form, activeFiles, inactiveFiles, initialActive.length, initialInactive.length]);

  const handleOk = async () => {
    const values = await form.validateFields();
    const payload: IconInfo = {
      id: editing?.id,
      name: values.name,
      icon_active: activeFiles[0]?.fileUrl || editing?.icon_active || '',
      icon: inactiveFiles[0]?.fileUrl || editing?.icon || '',
    };
    try {
      await addOrUpdateStoreIcon({
        id: payload.id,
        storeId: storeId as any,
        name: payload.name,
        selectedIcon: payload.icon_active,
        unselectedIcon: payload.icon,
      });
      message.success(editing?.id ? '编辑成功' : '新增成功');
      onOk?.({ ...payload, id: payload.id });
    } catch (err) {}
  };

  // 编辑态初始化见上方 initialActive/initialInactive

  return (
    <Modal
      width={720}
      title="新建图标"
      open={open}
      onCancel={onCancel}
      onOk={handleOk}
      okText="保存"
      cancelText="取消"
      okButtonProps={{ disabled: okDisabled }}
      destroyOnClose
    >
      <Form form={form} layout="vertical" preserve={false}>
        <Form.Item
          label="图标名称："
          name="name"
          rules={[{ required: true, message: '请输入图标名称' }]}
        >
          <Input placeholder="请输入" maxLength={8} showCount />
        </Form.Item>

        <div style={{ marginBottom: 8, fontWeight: 500 }}>上传图标：</div>
        <Typography.Text type="secondary">支持jpg/jpeg/png格式，建议尺寸100*100px</Typography.Text>

        <Divider style={{ margin: '12px 0 16px' }} />

        <Space size={40} align="start">
          <div style={{ textAlign: 'center' }}>
            <div>
              <FileUpload defaultValue={initialActive} maxCount={1} onChange={setActiveFiles} />
            </div>
            <div style={{ marginTop: 8, color: '#999' }}>选中状态</div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div>
              <FileUpload defaultValue={initialInactive} maxCount={1} onChange={setInactiveFiles} />
            </div>
            <div style={{ marginTop: 8, color: '#999' }}>未选中状态</div>
          </div>
        </Space>
      </Form>
    </Modal>
  );
};

export default CreateIconModal;


